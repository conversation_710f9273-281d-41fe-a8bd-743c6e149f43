---
title: Milestone M2 — Confidence & Audit Enhancements
description: Add full coverage / confidence metrics, unknown-edge detection, and automated audit reports for every KG update.
created: 2025-05-30
version: 0.1.0
status: Draft
tags: [milestone]
authors: [nitishMehrotra]
---

import { Callout } from '@/components/Callout'

<Callout emoji="🧮">
<strong>Goal:</strong> Turn the KG into a self-auditing asset.
Every <code>build-kg</code> / <code>sync-kg</code> run must produce a verifiable coverage & confidence report and flag “unknown edges” needing manual review.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
node: "20.11.0"
pnpm: "8.15.4"
typescript: "5.4.3"
simple-git: "3.22.0"
fast-json-stable-stringify: "2.1.0"
chalk: "5.3.0"     # CLI colouring
jest: "29.7.0"
```

---

## 🎯 Definition of Done

1. **Audit CLI**: `pnpm run audit-kg` produces:
   - `kg-audit.json` — diff of node/edge additions/removals, coverage %, unknown edges list.
   - Pretty console summary with colour (green ≥ 0.75, yellow 0.5-0.75, red < 0.5).
2. **Unknown Edges**: Any workflow_calls whose target function is missing or any implements edge whose spec/component is stale are emitted with confidence: 0.2 and listed in audit.
3. **CI**: CI job `kg-audit` fails if any milestone coverage < 0.5 or unknown-edge count > 10.
4. **Timestamps**: JSON-LD nodes now contain last_verified ISO timestamp each sync/audit run.
5. **Coverage**: Unit-test coverage ≥ 85 % for new audit code.
6. **Performance**: `pnpm run audit-kg -- --since HEAD~1` completes in ≤ 60 seconds on a repository with ≤ 2000 files (checked in CI with artificial file generator).

---

## 📦 Deliverables

| Artefact / Path                        | Content                                                      |
|----------------------------------------|--------------------------------------------------------------|
| code/packages/kg-audit-lib/            | coverage.ts, unknownEdges.ts, tests                          |
| code/packages/kg-cli/                  | new audit-kg.ts with bin entry                               |
| kg-audit.json                          | Generated diff & metrics (ignored in .gitignore)             |
| .github/workflows/kg-audit.yml         | CI job on PR & nightly main                                  |
| docs/tech-specs/domains/kg-audit.mdx   | Domain doc: formulas, thresholds                             |
| schemas/kg-audit.schema.json           | JSON Schema defining audit file (entities, metrics, unknownEdges[]) |

---

## 🗂 Directory Additions

```text
code/packages/
└─ kg-audit-lib/
   ├─ src/
   │   ├─ coverage.ts
   │   ├─ unknownEdges.ts
   │   └─ index.ts
   ├─ tests/
   └─ package.json
```

---

## 🧠 Key Decisions

| Topic                 | Decision / Formula                                                                                              | Rationale |
|-----------------------|-----------------------------------------------------------------------------------------------------------------|-----------|
| Coverage metric       | `implemented_edges / total_required_edges` per milestone.                                                       | Simple, language-agnostic. |
| Confidence decay      | **Pseudocode:**<br/>```ts
weeks = floor(daysSinceVerified / 7)
confidence = max(1.0 - 0.1 * weeks, 0.2)
``` | Linear decay; never below 0.2. |
| Unknown-edge cap      | Build fails if **unknown edges > 10** *or* **increase > 20 % vs previous audit**.                               | Prevent silent drift. |
| Report schema         | `kg-audit.schema.json` (JSON Schema Draft 2020-12) committed for machine validation.                            | Agents & tests can validate structure. |
| Report storage        | `kg-audit.json` is *not* committed; stored as CI artefact only.                                                 | Keeps Git clean. |
| Performance guardrail | Audit must finish **≤ 60 s** on a repo with ≤ 2000 source files.                                                | Ensures CI remains fast. |

---

## 🛠️ Technical Specifications

### 📝 Audit Report Generation Algorithm

**Input**: Knowledge Graph (KG) JSON-LD, git diff, previous audit file (optional)
**Output**: Audit report JSON (kg-audit.json)

**Steps:**
1. **Extract Edges/Nodes**: Parse KG for all nodes and edges.
2. **Calculate Coverage**: For each milestone, compute implemented/required edges.
3. **Detect Unknown Edges**: Identify workflow_calls with missing targets and stale implements edges.
4. **Apply Confidence Decay**: Update confidence based on last_verified timestamp.
5. **Generate Summary**: Include generatedAt, edgeTotals, milestone metrics, unknownEdges.
6. **Validate Schema**: Ensure output matches kg-audit.schema.json.
7. **Performance**: Complete in ≤ 60 seconds for ≤ 2000 files.

**Error Handling:**
- Fail CI if coverage < 0.5 or unknown edges > 10.
- Log and skip malformed nodes/edges.

### 📦 Output Interfaces

```typescript
interface AuditReport {
  summary: {
    generatedAt: string;
    edgeTotals: Record<string, number>;
  };
  milestones: Array<{
    id: string;
    coverage: number;
    confidence: number;
  }>;
  unknownEdges: string[];
}
```

---

## 🔨 Task Breakdown

| #   | Branch                | Task                                         | Owner |
|-----|-----------------------|----------------------------------------------|-------|
| 01  | m2/audit-lib          | Scaffold kg-audit-lib                        | BE    |
| 01a | m2/audit-schema       | Commit schemas/kg-audit.schema.json; add jest validation util | PM |
| 02  | m2/coverage           | Implement coverage calc                      | BE    |
| 03  | m2/unknown-edges      | Implement detection logic                    | BE    |
| 04  | m2/cli                | Add audit-kg.ts cmd to kg-cli                | BE    |
| 05  | m2/tests              | Jest tests (fixtures change graphs)          | BE    |
| 06  | m2/ci                 | Add kg-audit.yml workflow                    | DevOps|
| 07  | m2/domain-doc         | Write kg-audit.mdx                           | PM    |
| 08  | m2/spec-quality       | Run spec-lint & approve spec                 | PM    |
| 09  | m2/final-tag          | Merge & tag kg-audit-v2.0.0                  | Lead  |

---

## 🤖 CLI Specification (audit-kg)

**Usage:**

```bash
pnpm run audit-kg [--since <commit-ish>] [--format json|pretty|both] [--fail-under 0.5]
```

- Default `--since` = last commit on branch versus origin/main.
- Outputs summary then writes kg-audit.json (unless --format pretty).
- Exit codes: 0 = OK, 61 = coverage breach, 62 = unknown-edge cap, 1 = error.

---

## 🤖 CI Workflow (.github/workflows/kg-audit.yml)

```yaml
name: KG Audit
on:
  pull_request:
  schedule:
    - cron: '0 3 * * *'   # nightly UTC

jobs:
  audit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with: { fetch-depth: 0 }
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && pnpm install
      - run: pnpm run audit-kg -- --since origin/main --format pretty
# Job fails on non-zero exit status.
```

---

## 🧪 Acceptance Tests

### 1️⃣ Coverage calc

```bash
# Remove one @implements annotation → audit shows drop.
```

### 2️⃣ Unknown edge

```bash
# Rename a called function → audit flags unknown edge.
```

### 3️⃣ Threshold fail

```bash
pnpm run audit-kg -- --fail-under 0.9  # CLI exit 61
```

### 4️⃣ CI green

```bash
# CI green on clean branch.
```

---

## ✅ Success Criteria

- [ ] **SC-1:** Audit prints summary.
- [ ] **SC-2:** Unknown edges & coverage appear in kg-audit.json.
- [ ] **SC-3:** CI job enforces thresholds.
- [ ] **SC-4:** Tests ≥ 85 % coverage.
- [ ] **SC-5:** Spec passes spec-lint.
- [ ] **SC-6:** Merge → tag kg-audit-v2.0.0

---

## 🔄 Document History

| Version | Date       | Changes                                   | Author            |
|---------|------------|-------------------------------------------|-------------------|
| 0.1.0   | 2025-05-30 | Initial milestone specification           | nitishMehrotra    |
| 0.2.0   | 2025-06-02 | Refactored for structure, clarity, parity | nitishMehrotra    |

---

## 📄 JSON Schema Reference

### kg-audit.schema.json

```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "$id": "https://workflow-mapper.dev/schemas/kg-audit.schema.json",
  "title": "Knowledge-Graph Audit Report",
  "type": "object",
  "required": ["summary", "milestones", "unknownEdges"],
  "properties": {
    "summary": {
      "type": "object",
      "required": ["generatedAt", "edgeTotals"],
      "properties": {
        "generatedAt": { "type": "string", "format": "date-time" },
        "edgeTotals":  { "type": "object" }
      }
    },
    "milestones": {
      "type": "array",
      "items": {
        "type": "object",
        "required": ["id", "coverage", "confidence"],
        "properties": {
          "id":         { "type": "string" },
          "coverage":   { "type": "number", "minimum": 0, "maximum": 1 },
          "confidence": { "type": "number", "minimum": 0, "maximum": 1 }
        }
      }
    },
    "unknownEdges": {
      "type": "array",
      "items": { "type": "string" }
    }
  }
}
```

> Agents must validate kg-audit.json against this schema during tests.